import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/novel_agent_controller.dart';
import '../models/novel.dart';
import '../widgets/novel_agent_panel.dart';

/// 小说 Agent 功能演示页面
class NovelAgentDemo extends StatefulWidget {
  const NovelAgentDemo({super.key});

  @override
  State<NovelAgentDemo> createState() => _NovelAgentDemoState();
}

class _NovelAgentDemoState extends State<NovelAgentDemo> {
  final NovelAgentController _agentController = Get.put(NovelAgentController());
  
  @override
  void initState() {
    super.initState();
    _initializeDemo();
  }

  void _initializeDemo() async {
    // 创建一个演示小说
    final demoNovel = Novel(
      id: 'demo_novel',
      title: '演示小说：《星际穿越》',
      genre: '科幻',
      outline: '一个关于星际探索和时空穿越的故事。主人公是一名宇航员，在执行任务时意外穿越到了另一个时空。',
      chapters: [
        Chapter(
          number: 1,
          title: '启程',
          content: '''第一章 启程

2157年，地球联邦太空总署。

李明站在巨大的观景窗前，凝视着远方的星空。作为"星际探索者号"的船长，他即将带领团队执行人类历史上最重要的任务——寻找适合人类居住的新星球。

"船长，所有系统检查完毕，随时可以出发。"副船长陈雨走到他身边，递上了一份报告。

李明接过报告，快速浏览了一遍。三年的准备，无数次的模拟训练，终于到了这一刻。

"通知全体船员，十分钟后启程。"李明的声音在舰桥内回响。

窗外，蔚蓝的地球正在缓缓转动，那是他们的家园，也是他们要拯救的世界。''',
        ),
        Chapter(
          number: 2,
          title: '异常',
          content: '''第二章 异常

航行进入第三个月，一切都按计划进行。

"船长，我们检测到前方有异常的时空波动。"导航员小王紧张地报告。

李明快步走到控制台前，屏幕上显示着一团奇异的蓝色光芒，它在太空中缓缓旋转，散发着神秘的能量。

"这是什么？"陈雨皱着眉头问道。

"从来没有见过这种现象。"科学官张博士调出了各种数据，"能量读数超出了我们的理解范围。"

就在这时，那团蓝色光芒突然扩大，将整艘飞船包围了起来。

"警报！警报！时空坐标发生异常！"

李明感到一阵眩晕，眼前的一切都开始扭曲变形...''',
        ),
      ],
      useFileSystem: false,
    );

    // 设置演示小说到 Agent 控制器
    await _agentController.setCurrentNovel(demoNovel);
    _agentController.showAgentPanel.value = true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('小说 Agent 功能演示'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Obx(() {
        return Row(
          children: [
            // 左侧内容区域
            Expanded(
              flex: 2,
              child: _buildDemoContent(),
            ),
            
            // 右侧 Agent 面板
            if (_agentController.showAgentPanel.value && 
                _agentController.currentNovel.value != null)
              NovelAgentPanel(
                novel: _agentController.currentNovel.value!,
                width: 400,
              ),
          ],
        );
      }),
    );
  }

  Widget _buildDemoContent() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            '🤖 岱宗AI辅助创作助手演示',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 功能介绍
          _buildFeatureCard(
            icon: Icons.chat_bubble_outline,
            title: '聊天模式',
            description: '与AI助手对话，获取创作建议和灵感',
            examples: [
              '分析人物性格',
              '讨论情节发展',
              '获取写作技巧',
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildFeatureCard(
            icon: Icons.edit,
            title: '创作模式',
            description: 'AI直接编辑和修改章节内容，提供具体的修改建议',
            examples: [
              '优化对话内容',
              '增加环境描写',
              '调整叙述节奏',
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildFeatureCard(
            icon: Icons.link,
            title: '@ 语法引用',
            description: '使用@第X章来引用其他章节内容',
            examples: [
              '@第1章 分析主人公性格',
              '@第2章 这里的情节如何发展？',
              '结合@第1章的设定，续写故事',
            ],
          ),
          
          const SizedBox(height: 32),
          
          // 操作提示
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.lightbulb, color: Colors.blue[700]),
                    const SizedBox(width: 8),
                    Text(
                      '试试这些指令：',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildSuggestionChip('分析第一章的写作风格'),
                _buildSuggestionChip('优化第二章的对话'),
                _buildSuggestionChip('@第1章 主人公的性格特点是什么？'),
                _buildSuggestionChip('为第三章写一个开头'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required List<String> examples,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                color: Colors.grey[600],
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: examples.map((example) => 
                Chip(
                  label: Text(
                    example,
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                ),
              ).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestionChip(String text) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          // 这里可以添加点击后自动填入输入框的功能
          Get.snackbar(
            '提示',
            '请在右侧AI助手面板中输入: $text',
            duration: const Duration(seconds: 2),
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.touch_app, size: 14, color: Colors.blue[600]),
              const SizedBox(width: 4),
              Text(
                text,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
