// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'novel.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NovelAdapter extends TypeAdapter<Novel> {
  @override
  final int typeId = 0;

  @override
  Novel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };

    // 兼容旧格式（10个字段）和新格式（12个字段）
    if (numOfFields == 10) {
      // 旧格式：没有folderPath和useFileSystem字段
      return Novel(
        id: fields[0] as String,
        title: fields[1] as String,
        genre: fields[2] as String,
        outline: fields[3] as String,
        content: fields[4] as String,
        chapters: (fields[5] as List).cast<Chapter>(),
        createdAt: fields[6] as DateTime,
        updatedAt: fields[7] as DateTime?,
        style: fields[8] as String?,
        sessionId: fields[9] as String?,
        folderPath: null, // 旧数据没有这个字段
        useFileSystem: false, // 旧数据默认为false
      );
    } else {
      // 新格式：包含所有字段
      return Novel(
        id: fields[0] as String,
        title: fields[1] as String,
        genre: fields[2] as String,
        outline: fields[3] as String,
        content: fields[4] as String,
        chapters: (fields[5] as List).cast<Chapter>(),
        createdAt: fields[6] as DateTime,
        updatedAt: fields[7] as DateTime?,
        style: fields[8] as String?,
        sessionId: fields[9] as String?,
        folderPath: fields[10] as String?,
        useFileSystem: fields[11] as bool? ?? false,
      );
    }
  }

  @override
  void write(BinaryWriter writer, Novel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.genre)
      ..writeByte(3)
      ..write(obj.outline)
      ..writeByte(4)
      ..write(obj.content)
      ..writeByte(5)
      ..write(obj.chapters)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt)
      ..writeByte(8)
      ..write(obj.style)
      ..writeByte(9)
      ..write(obj.sessionId)
      ..writeByte(10)
      ..write(obj.folderPath)
      ..writeByte(11)
      ..write(obj.useFileSystem);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NovelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
