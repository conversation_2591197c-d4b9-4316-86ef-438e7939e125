# 岱宗AI辅助助手功能说明

## 概述

岱宗AI辅助助手是对原有AI编辑器的全面升级版本，专门为小说创作提供智能化的编辑建议和内容优化功能。

## 主要改进

### 1. 功能重命名
- **原名称**: AI编辑器
- **新名称**: 岱宗AI辅助助手
- **涉及范围**: 
  - 界面上所有显示的文本标签
  - 按钮的tooltip提示文本
  - 相关的类名和方法名
  - 用户界面提示信息

### 2. 增强的编辑功能

#### 2.1 智能修改建议生成
- **功能**: 点击"生成修改建议"按钮，AI会分析当前章节内容并提供具体的修改建议
- **特点**: 
  - 针对特定段落或句子的局部修改
  - 避免整体重写，保持原文风格
  - 提供修改理由说明

#### 2.2 修改建议展示
- **可视化对比**: 清晰展示原文和AI建议的修改内容
- **建议详情**: 每个建议包含：
  - 需要修改的原文段落
  - AI建议的改进文字
  - 修改理由说明
  - 文本位置信息

#### 2.3 交互式修改管理
- **单个操作**: 
  - 接受单个修改建议
  - 拒绝单个修改建议
- **批量操作**:
  - 应用全部建议
  - 拒绝全部建议
  - 清空所有建议

### 3. 用户界面改进

#### 3.1 新增修改建议区域
- 位于AI助手面板顶部
- 动态显示/隐藏
- 支持滚动查看多个建议

#### 3.2 建议状态可视化
- **待处理**: 白色背景，显示操作按钮
- **已应用**: 绿色背景，标记为已应用
- **已拒绝**: 红色背景，标记为已拒绝

#### 3.3 增强的AI面板
- 新的标题栏设计
- 集成的操作菜单
- 改进的聊天界面

## 技术实现

### 1. 核心组件

#### DaizongAIAssistantScreen
- 替代原有的NovelEditWithAIScreen
- 集成修改建议功能
- 保持与现有系统的兼容性

#### ChapterEditSuggestion模型
```dart
class ChapterEditSuggestion {
  final String id;
  final String originalText;
  final String suggestedText;
  final String reason;
  final int startIndex;
  final int endIndex;
  final DateTime timestamp;
  bool isApplied;
  bool isRejected;
}
```

### 2. AI交互流程

1. **生成建议**: 构建专门的提示词发送给AI
2. **解析响应**: 从AI响应中提取JSON格式的建议
3. **展示建议**: 在界面中可视化展示修改建议
4. **应用修改**: 直接修改章节内容并保存

### 3. 提示词设计

AI使用专门设计的提示词来生成修改建议：
- 包含小说背景信息
- 指定返回JSON格式
- 要求提供具体的修改位置和理由

## 使用方法

### 1. 打开岱宗AI辅助助手
1. 在"我的书库"中找到要编辑的小说
2. 点击小说项目右侧的"岱宗AI辅助助手"按钮
3. 或通过右键菜单选择"岱宗AI辅助助手"

### 2. 生成修改建议
1. 选择要编辑的章节
2. 点击顶部工具栏的"生成修改建议"按钮
3. 等待AI分析并生成建议

### 3. 管理修改建议
1. 查看AI面板顶部的修改建议区域
2. 阅读每个建议的详细信息
3. 选择接受、拒绝或批量操作

### 4. 保存更改
1. 应用满意的修改建议
2. 点击"保存"按钮保存章节内容
3. 修改会自动同步到小说数据

## 兼容性

### 1. 现有功能保持
- 完全兼容现有的SmartComposerController
- 保持与AI模型配置系统的集成
- 支持所有现有的AI提供商

### 2. 数据兼容
- 使用现有的Novel和Chapter数据模型
- 保持与文件系统存储的兼容
- 不影响现有小说数据

## 未来扩展

### 1. 计划功能
- 撤销已应用的修改
- 修改建议的历史记录
- 更精细的修改类型分类
- 自定义修改建议规则

### 2. 性能优化
- 建议生成的缓存机制
- 批量处理优化
- 界面响应性改进

## 注意事项

1. **AI模型配置**: 使用前需要确保已正确配置AI模型
2. **网络连接**: 生成建议需要网络连接到AI服务
3. **内容备份**: 建议在应用大量修改前备份原始内容
4. **模型限制**: 修改建议的质量取决于所使用的AI模型

## 故障排除

### 常见问题
1. **无法生成建议**: 检查AI模型配置和网络连接
2. **建议解析失败**: AI响应格式不正确，可能需要重试
3. **修改应用失败**: 检查文本匹配和文件权限

### 错误处理
- 所有操作都有错误提示
- 失败操作不会影响原始内容
- 支持重试机制
