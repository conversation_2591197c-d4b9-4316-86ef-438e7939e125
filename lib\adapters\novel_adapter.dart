import 'package:hive/hive.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/chapter.dart' as chapter_model;

class NovelAdapter extends TypeAdapter<Novel> {
  @override
  final int typeId = 0;

  @override
  Novel read(BinaryReader reader) {
    try {
      final numOfFields = reader.readByte();
      final fields = <int, dynamic>{
        for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
      };

      // 确保必要字段存在
      if (!fields.containsKey(0) ||
          !fields.containsKey(1) ||
          !fields.containsKey(2) ||
          !fields.containsKey(3) ||
          !fields.containsKey(4) ||
          !fields.containsKey(5) ||
          !fields.containsKey(6)) {
        print('[ERROR] Novel数据缺少必要字段');
        throw Exception('Novel数据缺少必要字段');
      }

      // 处理chapters字段，确保它是List<Chapter>类型
      List<Chapter> chapters = [];
      if (fields[5] != null) {
        if (fields[5] is List) {
          try {
            chapters = (fields[5] as List).map((item) {
              if (item is Chapter) {
                return item;
              } else if (item is Map) {
                try {
                  return Chapter.fromJson(Map<String, dynamic>.from(item));
                } catch (e) {
                  print('[WARNING] 从Map解析Chapter失败: $e');
                  return Chapter(number: 0, title: '解析错误', content: '');
                }
              } else {
                print('[WARNING] 无法解析章节数据: $item');
                return Chapter(number: 0, title: '解析错误', content: '');
              }
            }).toList();
          } catch (e) {
            print('[ERROR] 解析章节列表失败: $e');
            // 如果解析失败，使用空列表
            chapters = [];
          }
        } else {
          print('[WARNING] chapters字段不是List类型: ${fields[5].runtimeType}');
        }
      }

      return Novel(
        id: fields[0] as String? ?? '',
        title: fields[1] as String? ?? '未命名小说',
        genre: fields[2] as String? ?? '',
        outline: fields[3] as String? ?? '',
        content: fields[4] as String? ?? '',
        chapters: chapters,
        createdAt: fields[6] as DateTime? ?? DateTime.now(),
        updatedAt: fields[7] as DateTime?,
        style: fields[8] as String?,
        sessionId: fields.containsKey(9) ? fields[9] as String? : null,
        folderPath: fields.containsKey(10) ? fields[10] as String? : null,
        useFileSystem: fields.containsKey(11) ? (fields[11] as bool? ?? false) : false,
      );
    } catch (e, stackTrace) {
      print('[ERROR] 读取Novel数据失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');
      // 返回一个默认的Novel对象，避免应用崩溃
      return Novel(
        title: '读取错误',
        genre: '',
        outline: '',
        content: '数据读取失败: $e',
        chapters: [],
        createdAt: DateTime.now(),
        folderPath: null,
        useFileSystem: false,
      );
    }
  }

  @override
  void write(BinaryWriter writer, Novel obj) {
    try {
      writer.writeByte(12); // 更新为12个字段，包括folderPath和useFileSystem
      writer.writeByte(0);
      writer.write(obj.id);
      writer.writeByte(1);
      writer.write(obj.title);
      writer.writeByte(2);
      writer.write(obj.genre);
      writer.writeByte(3);
      writer.write(obj.outline);
      writer.writeByte(4);
      writer.write(obj.content);
      writer.writeByte(5);
      writer.write(obj.chapters);
      writer.writeByte(6);
      writer.write(obj.createdAt);
      writer.writeByte(7);
      writer.write(obj.updatedAt);
      writer.writeByte(8);
      writer.write(obj.style);
      writer.writeByte(9);
      writer.write(obj.sessionId);
      writer.writeByte(10);
      writer.write(obj.folderPath);
      writer.writeByte(11);
      writer.write(obj.useFileSystem);

      print('[DEBUG] Novel数据写入成功: ${obj.title}');
    } catch (e, stackTrace) {
      print('[ERROR] 写入Novel数据失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');
      rethrow;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NovelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
