import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/novel_agent_service.dart';

/// 小说 Agent Diff 视图
/// 类似 Cursor IDE 的差异对比视图，显示编辑建议的详细对比
class NovelAgentDiffView extends StatefulWidget {
  final EditSuggestion suggestion;
  final VoidCallback? onApply;
  final VoidCallback? onReject;
  final bool isCompact;

  const NovelAgentDiffView({
    super.key,
    required this.suggestion,
    this.onApply,
    this.onReject,
    this.isCompact = false,
  });

  @override
  State<NovelAgentDiffView> createState() => _NovelAgentDiffViewState();
}

class _NovelAgentDiffViewState extends State<NovelAgentDiffView> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          if (_isExpanded || !widget.isCompact) _buildDiffContent(),
          _buildActions(),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getEditTypeIcon(),
            size: 16,
            color: _getEditTypeColor(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.suggestion.description,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),
          if (widget.isCompact)
            IconButton(
              icon: Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                size: 20,
              ),
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          _buildLocationInfo(),
        ],
      ),
    );
  }

  /// 构建位置信息
  Widget _buildLocationInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '第${widget.suggestion.targetChapter.number}章 行${widget.suggestion.startLine + 1}-${widget.suggestion.endLine + 1}',
        style: TextStyle(
          fontSize: 10,
          color: Colors.blue[700],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建差异内容
  Widget _buildDiffContent() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.suggestion.originalText.isNotEmpty) ...[
            _buildDiffSection(
              title: '原文',
              content: widget.suggestion.originalText,
              color: Colors.red,
              icon: Icons.remove,
              isRemoval: true,
            ),
            const SizedBox(height: 12),
          ],
          _buildDiffSection(
            title: widget.suggestion.type == EditType.insert ? '插入内容' : '修改后',
            content: widget.suggestion.suggestedText,
            color: Colors.green,
            icon: widget.suggestion.type == EditType.insert ? Icons.add : Icons.edit,
            isRemoval: false,
          ),
        ],
      ),
    );
  }

  /// 构建差异部分
  Widget _buildDiffSection({
    required String title,
    required String content,
    required Color color,
    required IconData icon,
    required bool isRemoval,
  }) {
    final lines = content.split('\n');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: _getColorShade(color, 600), size: 16),
            const SizedBox(width: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: _getColorShade(color, 600),
              ),
            ),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.copy, size: 14),
              onPressed: () => _copyToClipboard(content),
              tooltip: '复制',
              constraints: const BoxConstraints(
                minWidth: 24,
                minHeight: 24,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: widget.isCompact ? 100 : 200,
          ),
          decoration: BoxDecoration(
            color: color.withOpacity(0.05),
            border: Border.all(color: color.withOpacity(0.2)),
            borderRadius: BorderRadius.circular(4),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: lines.asMap().entries.map((entry) {
                final lineNumber = entry.key + 1;
                final lineContent = entry.value;
                
                return _buildDiffLine(
                  lineNumber: lineNumber,
                  content: lineContent,
                  color: color,
                  isRemoval: isRemoval,
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建差异行
  Widget _buildDiffLine({
    required int lineNumber,
    required String content,
    required Color color,
    required bool isRemoval,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 行号
          Container(
            width: 30,
            padding: const EdgeInsets.only(right: 8),
            child: Text(
              lineNumber.toString(),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[500],
                fontFamily: 'monospace',
              ),
            ),
          ),
          // 变更标记
          Container(
            width: 16,
            child: Text(
              isRemoval ? '-' : '+',
              style: TextStyle(
                fontSize: 12,
                color: _getColorShade(color, 600),
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),
          ),
          // 内容
          Expanded(
            child: Text(
              content.isEmpty ? ' ' : content,
              style: TextStyle(
                fontSize: 12,
                color: _getColorShade(color, 700),
                fontFamily: 'monospace',
                decoration: isRemoval ? TextDecoration.lineThrough : null,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[25],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          // 统计信息
          Expanded(
            child: Text(
              _buildStatsText(),
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
            ),
          ),
          
          // 操作按钮
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton.icon(
                onPressed: widget.onApply,
                icon: const Icon(Icons.check, size: 16),
                label: const Text('应用'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(80, 32),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
              const SizedBox(width: 8),
              OutlinedButton.icon(
                onPressed: widget.onReject,
                icon: const Icon(Icons.close, size: 16),
                label: const Text('拒绝'),
                style: OutlinedButton.styleFrom(
                  minimumSize: const Size(80, 32),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取编辑类型图标
  IconData _getEditTypeIcon() {
    switch (widget.suggestion.type) {
      case EditType.replace:
        return Icons.edit;
      case EditType.insert:
        return Icons.add;
      case EditType.delete:
        return Icons.remove;
    }
  }

  /// 获取编辑类型颜色
  Color _getEditTypeColor() {
    switch (widget.suggestion.type) {
      case EditType.replace:
        return Colors.blue;
      case EditType.insert:
        return Colors.green;
      case EditType.delete:
        return Colors.red;
    }
  }

  /// 构建统计文本
  String _buildStatsText() {
    final originalLines = widget.suggestion.originalText.split('\n').length;
    final suggestedLines = widget.suggestion.suggestedText.split('\n').length;
    
    switch (widget.suggestion.type) {
      case EditType.replace:
        return '替换 $originalLines 行 → $suggestedLines 行';
      case EditType.insert:
        return '插入 $suggestedLines 行';
      case EditType.delete:
        return '删除 $originalLines 行';
    }
  }

  /// 复制到剪贴板
  void _copyToClipboard(String content) {
    Clipboard.setData(ClipboardData(text: content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已复制到剪贴板'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  /// 获取颜色的深浅变化
  Color _getColorShade(Color color, int shade) {
    if (color is MaterialColor) {
      return color[shade] ?? color;
    }

    // 对于普通颜色，手动计算深浅变化
    switch (shade) {
      case 600:
        return Color.lerp(color, Colors.black, 0.2) ?? color;
      case 700:
        return Color.lerp(color, Colors.black, 0.3) ?? color;
      default:
        return color;
    }
  }
}
