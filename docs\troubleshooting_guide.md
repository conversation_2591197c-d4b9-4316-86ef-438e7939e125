# AI文件编辑器故障排除指南

## 常见错误及解决方案

### 1. TextEditingController错误

**错误信息**: `A TextEditingController was used after being disposed`

**原因**: 组件销毁后仍有监听器在尝试更新已释放的控制器

**解决方案**:
- 确保在dispose方法中正确释放所有监听器
- 在更新控制器前检查组件是否仍然mounted
- 使用Worker来管理GetX监听器的生命周期

**修复代码示例**:
```dart
class _MyWidgetState extends State<MyWidget> {
  Worker? _worker;
  
  @override
  void initState() {
    super.initState();
    _worker = ever(controller.someValue, (value) {
      if (mounted) {
        // 安全地更新UI
      }
    });
  }
  
  @override
  void dispose() {
    _worker?.dispose();
    super.dispose();
  }
}
```

### 2. OpenAI API调用失败

#### 2.1 404错误

**错误信息**: `OpenAI API 调用失败: 404`

**可能原因**:
1. API端点URL不正确
2. 模型名称不存在
3. API版本不匹配

**解决方案**:
1. 检查API端点URL是否正确
   - OpenAI: `https://api.openai.com/v1`
   - Azure OpenAI: `https://your-resource.openai.azure.com/`
2. 验证模型名称是否正确
   - 常用模型: `gpt-4`, `gpt-3.5-turbo`
3. 确认API版本兼容性

#### 2.2 401错误

**错误信息**: `OpenAI API 调用失败: 401`

**原因**: API密钥无效或未配置

**解决方案**:
1. 检查API密钥是否正确
2. 确认API密钥有效期
3. 验证API密钥权限

#### 2.3 429错误

**错误信息**: `OpenAI API 调用失败: 429`

**原因**: API调用频率过高

**解决方案**:
1. 降低调用频率
2. 检查账户配额
3. 考虑升级API计划

### 3. 文件编辑相关错误

#### 3.1 文件无法打开

**可能原因**:
1. 文件路径不正确
2. 文件权限不足
3. 文件不存在

**解决方案**:
1. 验证文件路径
2. 检查文件权限
3. 使用绝对路径

#### 3.2 文件保存失败

**可能原因**:
1. 磁盘空间不足
2. 文件被其他程序占用
3. 权限不足

**解决方案**:
1. 检查磁盘空间
2. 关闭占用文件的程序
3. 以管理员权限运行

### 4. AI编辑响应解析失败

**错误信息**: `解析AI响应失败`

**可能原因**:
1. AI返回的格式不符合预期
2. JSON格式错误
3. 网络传输问题

**解决方案**:
1. 检查系统提示是否正确
2. 使用更稳定的AI模型
3. 添加重试机制

## 配置检查清单

### 基本配置
- [ ] API密钥已正确配置
- [ ] API端点URL正确
- [ ] 模型名称有效
- [ ] 网络连接正常

### 高级配置
- [ ] 代理设置（如需要）
- [ ] 超时设置合理
- [ ] 最大令牌数适当
- [ ] 温度参数合理

## 调试技巧

### 1. 启用详细日志
在开发模式下启用详细日志输出：
```dart
if (kDebugMode) {
  print('API请求: $requestBody');
  print('API响应: $responseBody');
}
```

### 2. 使用网络抓包工具
使用Charles、Fiddler等工具监控API请求：
- 检查请求格式
- 验证响应内容
- 分析错误原因

### 3. 测试API连接
创建简单的测试用例验证API连接：
```dart
Future<void> testAPIConnection() async {
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/chat/completions'),
      headers: {
        'Authorization': 'Bearer $apiKey',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'model': 'gpt-3.5-turbo',
        'messages': [{'role': 'user', 'content': 'Hello'}],
        'max_tokens': 10,
      }),
    );
    print('API测试结果: ${response.statusCode}');
  } catch (e) {
    print('API测试失败: $e');
  }
}
```

## 性能优化建议

### 1. 减少API调用
- 合并多个小的编辑请求
- 使用缓存避免重复请求
- 实现请求去重

### 2. 优化文件操作
- 使用流式读写大文件
- 实现增量备份
- 定期清理临时文件

### 3. 改善用户体验
- 添加加载指示器
- 实现离线模式
- 提供操作撤销功能

## 联系支持

如果以上解决方案都无法解决您的问题，请：

1. 收集错误日志
2. 记录重现步骤
3. 提供配置信息
4. 联系技术支持

**注意**: 请勿在错误报告中包含API密钥等敏感信息。
