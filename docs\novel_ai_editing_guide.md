# 小说AI编辑功能使用指南

## 概述

小说AI编辑功能已集成到Smart Composer中，让您可以直接在聊天界面中编辑小说内容，就像与专业编辑对话一样自然。

## 功能特性

### 🎯 智能编辑模式
- **查看模式**: 与AI讨论小说创作，获取建议和灵感
- **编辑模式**: 直接编辑当前章节内容，AI会根据指令修改文本

### 📚 章节管理
- **章节选择**: 在下拉菜单中选择要编辑的章节
- **实时切换**: 随时切换不同章节进行编辑
- **字数统计**: 显示当前章节的字数

### ⚡ 快捷操作
- **续写**: 为当前章节续写内容
- **润色**: 优化文字表达，使其更加生动
- **改写**: 重新组织内容，提升质量

## 使用方法

### 1. 进入小说编辑
1. 在书库中找到要编辑的小说
2. 点击小说卡片上的"AI助手"按钮（🤖图标）
3. 或者点击小说菜单中的"AI聊天"选项

### 2. 选择编辑章节
1. 在聊天界面顶部的上下文栏中
2. 使用下拉菜单选择要编辑的章节
3. 当前章节信息会显示在界面上

### 3. 切换编辑模式
1. 点击上下文栏右侧的编辑按钮（👁️/✏️）
2. **查看模式**（👁️）: 普通聊天，讨论创作
3. **编辑模式**（✏️）: 直接编辑章节内容

### 4. 使用快捷操作
在编辑模式下，可以使用快捷按钮：
- **续写**: 点击"续写"按钮，AI会为当前章节续写内容
- **润色**: 点击"润色"按钮，AI会优化文字表达
- **改写**: 点击"改写"按钮，AI会重新组织内容

### 5. 自定义编辑指令
在编辑模式下，您可以输入自定义指令：
- "为这一章添加更多对话"
- "增加环境描写"
- "调整人物性格描述"
- "修改结尾，增加悬念"

## 编辑指令示例

### 续写类指令
```
请为这一章续写500字，主角发现了重要线索
继续写下去，描述两人的对话
为这个场景添加一个转折
```

### 润色类指令
```
请润色这一章的文字，使其更加生动
优化对话部分，让人物更有个性
改善环境描写，增强画面感
```

### 改写类指令
```
重新组织这一章的结构
改写开头，增加吸引力
调整叙述视角，改为第一人称
```

### 修改类指令
```
删除多余的描述，让节奏更紧凑
增加人物内心独白
修改对话，让冲突更激烈
调整情节发展，增加合理性
```

## 最佳实践

### 1. 明确的指令
- 使用具体、清晰的指令
- 说明期望的修改方向和程度
- 提供必要的背景信息

### 2. 分步编辑
- 一次专注一个方面（如对话、描写、情节）
- 先整体后细节
- 保持故事的连贯性

### 3. 保持风格一致
- 在指令中提及保持原有风格
- 注意人物性格的一致性
- 维护故事的整体基调

### 4. 合理使用AI建议
- AI的建议仅供参考
- 保持自己的创作主导权
- 根据需要调整AI的输出

## 注意事项

### ⚠️ 重要提醒
1. **备份重要内容**: 在大幅修改前建议备份
2. **逐步修改**: 避免一次性大幅改动
3. **保持原创性**: AI是辅助工具，不是替代品
4. **检查连贯性**: 修改后检查与其他章节的连贯性

### 🔧 技术要求
- 需要配置AI模型（OpenAI、Anthropic等）
- 确保网络连接稳定
- 建议使用较新的AI模型获得更好效果

## 故障排除

### 常见问题
1. **AI响应慢**: 检查网络连接和API配置
2. **编辑效果不理想**: 尝试更具体的指令
3. **章节选择问题**: 确认小说已正确加载

### 获取帮助
- 查看设置页面的AI配置
- 检查API密钥是否有效
- 尝试重新启动应用

## 更新日志

### v1.0.0
- 集成小说AI编辑功能到Smart Composer
- 支持章节选择和编辑模式切换
- 提供续写、润色、改写快捷操作
- 优化编辑指令处理和上下文管理

---

*这个功能让您的创作过程更加高效和有趣。AI是您的创作伙伴，帮助您将想法转化为精彩的故事！*
