import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import '../../models/novel.dart';
import '../../services/novel_file_manager.dart';
import '../../controllers/smart_composer_controller.dart';

/// 小说文件夹视图
/// 显示小说的文件夹结构，每个章节作为独立的 Markdown 文件
class NovelFolderView extends StatefulWidget {
  const NovelFolderView({super.key});

  @override
  State<NovelFolderView> createState() => _NovelFolderViewState();
}

class _NovelFolderViewState extends State<NovelFolderView> {
  final NovelFileManager _fileManager = NovelFileManager();
  final SmartComposerController _smartComposerController = Get.find<SmartComposerController>();
  
  Novel? _novel;
  String? _folderPath;
  List<FileSystemEntity> _files = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      _novel = args['novel'] as Novel?;
      _folderPath = args['folderPath'] as String?;
      
      if (_folderPath != null) {
        _loadFolderContents();
      }
    }
  }

  Future<void> _loadFolderContents() async {
    if (_folderPath == null) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final directory = Directory(_folderPath!);
      if (await directory.exists()) {
        final entities = await directory.list().toList();
        
        // 排序：文件夹在前，文件在后，按名称排序
        entities.sort((a, b) {
          if (a is Directory && b is File) return -1;
          if (a is File && b is Directory) return 1;
          return a.path.compareTo(b.path);
        });
        
        setState(() {
          _files = entities;
        });
      }
    } catch (e) {
      Get.snackbar('错误', '加载文件夹内容失败：$e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_novel?.title ?? '文件夹视图'),
        actions: [
          IconButton(
            icon: const Icon(Icons.smart_toy),
            tooltip: 'AI 写作助手',
            onPressed: () => _openSmartComposer(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '刷新',
            onPressed: _loadFolderContents,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'open_in_explorer':
                  _openInExplorer();
                  break;
                case 'export_markdown':
                  _exportAsMarkdown();
                  break;
                case 'sync_to_database':
                  _syncToDatabase();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'open_in_explorer',
                child: Row(
                  children: [
                    Icon(Icons.folder_open),
                    SizedBox(width: 8),
                    Text('在文件管理器中打开'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_markdown',
                child: Row(
                  children: [
                    Icon(Icons.description),
                    SizedBox(width: 8),
                    Text('导出为 Markdown'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sync_to_database',
                child: Row(
                  children: [
                    Icon(Icons.sync),
                    SizedBox(width: 8),
                    Text('同步到数据库'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildFileList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateFileDialog,
        tooltip: '新建文件',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildFileList() {
    if (_files.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '文件夹为空',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _files.length,
      itemBuilder: (context, index) {
        final file = _files[index];
        return _buildFileItem(file);
      },
    );
  }

  Widget _buildFileItem(FileSystemEntity entity) {
    final isDirectory = entity is Directory;
    final fileName = entity.path.split(Platform.pathSeparator).last;
    final isMarkdownFile = fileName.endsWith('.md');
    final isMetadataFile = fileName == 'metadata.json';
    
    IconData icon;
    Color iconColor;
    
    if (isDirectory) {
      icon = Icons.folder;
      iconColor = Colors.amber[700]!;
    } else if (isMarkdownFile) {
      icon = Icons.description;
      iconColor = Colors.blue[700]!;
    } else if (isMetadataFile) {
      icon = Icons.settings;
      iconColor = Colors.grey[600]!;
    } else {
      icon = Icons.insert_drive_file;
      iconColor = Colors.grey[600]!;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: iconColor),
        title: Text(fileName),
        subtitle: _buildFileSubtitle(entity),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isMarkdownFile && fileName != 'outline.md')
              IconButton(
                icon: const Icon(Icons.smart_toy),
                tooltip: 'AI 助手',
                onPressed: () => _openSmartComposerForFile(entity as File),
              ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleFileAction(value, entity),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'open',
                  child: Row(
                    children: [
                      Icon(Icons.open_in_new),
                      SizedBox(width: 8),
                      Text('打开'),
                    ],
                  ),
                ),
                if (isMarkdownFile)
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('编辑'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'rename',
                  child: Row(
                    children: [
                      Icon(Icons.drive_file_rename_outline),
                      SizedBox(width: 8),
                      Text('重命名'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete_outline),
                      SizedBox(width: 8),
                      Text('删除'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _openFile(entity),
      ),
    );
  }

  Widget _buildFileSubtitle(FileSystemEntity entity) {
    if (entity is File) {
      return FutureBuilder<FileStat>(
        future: entity.stat(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            final stat = snapshot.data!;
            final size = _formatFileSize(stat.size);
            final modified = _formatDate(stat.modified);
            return Text('$size • $modified');
          }
          return const Text('');
        },
      );
    } else {
      return const Text('文件夹');
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _openFile(FileSystemEntity entity) {
    if (entity is Directory) {
      // 导航到子文件夹
      Get.to(() => const NovelFolderView(), arguments: {
        'folderPath': entity.path,
      });
    } else if (entity is File) {
      final fileName = entity.path.split(Platform.pathSeparator).last;
      if (fileName.endsWith('.md')) {
        _openMarkdownFile(entity);
      } else {
        Get.snackbar('提示', '暂不支持打开此类型的文件');
      }
    }
  }

  void _openMarkdownFile(File file) {
    // TODO: 实现 Markdown 文件编辑器
    Get.snackbar('提示', 'Markdown 编辑器正在开发中');
  }

  void _handleFileAction(String action, FileSystemEntity entity) {
    switch (action) {
      case 'open':
        _openFile(entity);
        break;
      case 'edit':
        if (entity is File) {
          _openMarkdownFile(entity);
        }
        break;
      case 'rename':
        _showRenameDialog(entity);
        break;
      case 'delete':
        _showDeleteDialog(entity);
        break;
    }
  }

  void _showRenameDialog(FileSystemEntity entity) {
    final currentName = entity.path.split(Platform.pathSeparator).last;
    final controller = TextEditingController(text: currentName);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重命名'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '新名称',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _renameFile(entity, controller.text);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(FileSystemEntity entity) {
    final fileName = entity.path.split(Platform.pathSeparator).last;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除确认'),
        content: Text('确定要删除 "$fileName" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteFile(entity);
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showCreateFileDialog() {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('新建文件'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '文件名（如：第001章_新的开始.md）',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _createFile(controller.text);
            },
            child: const Text('创建'),
          ),
        ],
      ),
    );
  }

  Future<void> _renameFile(FileSystemEntity entity, String newName) async {
    // TODO: 实现文件重命名
    Get.snackbar('提示', '重命名功能正在开发中');
  }

  Future<void> _deleteFile(FileSystemEntity entity) async {
    // TODO: 实现文件删除
    Get.snackbar('提示', '删除功能正在开发中');
  }

  Future<void> _createFile(String fileName) async {
    // TODO: 实现文件创建
    Get.snackbar('提示', '创建文件功能正在开发中');
  }

  void _openSmartComposer() {
    Get.snackbar('提示', 'AI写作助手功能已移除，请使用AI编辑器');
  }

  void _openSmartComposerForFile(File file) {
    // TODO: 为特定文件打开 AI 助手
    Get.snackbar('提示', '文件级 AI 助手正在开发中');
  }

  void _openInExplorer() {
    // TODO: 在系统文件管理器中打开
    Get.snackbar('提示', '此功能需要平台特定实现');
  }

  void _exportAsMarkdown() {
    // TODO: 导出为单个 Markdown 文件
    Get.snackbar('提示', '导出功能正在开发中');
  }

  void _syncToDatabase() {
    // TODO: 同步文件夹内容到数据库
    Get.snackbar('提示', '同步功能正在开发中');
  }
}
