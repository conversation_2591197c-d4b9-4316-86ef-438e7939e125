# 岱宗AI辅助助手双模式功能说明

## 概述

岱宗AI辅助助手现在支持两种工作模式：**聊天模式**和**创作模式**，为用户提供不同层次的AI辅助体验。

## 功能特性

### 1. 模式切换机制

#### 1.1 切换界面
- **位置**: AI助手面板标题栏下方
- **样式**: 双选项卡式切换器
- **视觉指示**: 
  - 聊天模式：聊天气泡图标 + 蓝色高亮
  - 创作模式：编辑图标 + 蓝色高亮
- **默认模式**: 聊天模式

#### 1.2 切换效果
- 流畅的动画过渡
- 即时的界面元素更新
- 明确的状态提示

### 2. 聊天模式功能

#### 2.1 核心特性
- **AI角色**: 顾问和建议者
- **交互方式**: 传统对话交互
- **修改权限**: AI不直接修改章节内容
- **用户控制**: 用户手动复制建议并自行修改

#### 2.2 功能包含
- 生成修改建议按钮
- 修改建议展示（JSON格式解析）
- 建议卡片显示（原文/建议/理由）
- 单个建议的接受/拒绝操作

#### 2.3 输入提示
- "询问岱宗AI助手..."

### 3. 创作模式功能

#### 3.1 核心特性
- **AI角色**: 直接编辑助手
- **交互方式**: 指令式交互
- **修改权限**: AI可直接修改章节内容
- **用户控制**: 预览并确认修改

#### 3.2 支持的编辑指令
- **删除类**: "删除环境描写"、"去掉多余的形容词"
- **优化类**: "优化对话部分"、"改善文字流畅度"
- **增强类**: "增强人物情感描述"、"丰富场景细节"
- **调整类**: "调整语言风格"、"修改叙述视角"

#### 3.3 输入提示
- "输入编辑指令，如'删除环境描写'、'优化对话'等..."

### 4. 修改展示和确认机制

#### 4.1 聊天区域显示
- **修改消息气泡**: 橙色边框，编辑图标
- **修改详情**: 显示修改数量和类型
- **修改卡片**: 每个修改的详细信息
- **批量操作**: "全部接受"和"全部拒绝"按钮

#### 4.2 修改卡片内容
- **修改类型标签**: 删除、优化、增强等
- **修改理由**: AI提供的修改说明
- **原文显示**: 红色背景的原始文本
- **修改后显示**: 绿色背景的修改文本
- **单独操作**: 每个修改的接受/拒绝按钮

#### 4.3 操作按钮布局
- **聊天模式**: 生成建议按钮
- **创作模式**: 
  - 无待处理修改时：无额外按钮
  - 有待处理修改时：全部接受/全部拒绝按钮

### 5. 交互流程

#### 5.1 聊天模式流程
1. 用户点击"生成修改建议"
2. AI分析章节内容
3. 返回JSON格式的修改建议
4. 在聊天中显示建议卡片
5. 用户选择接受/拒绝单个建议
6. 接受的建议应用到章节内容

#### 5.2 创作模式流程
1. 用户输入编辑指令
2. AI分析指令和章节内容
3. 执行相应的文本修改
4. 返回JSON格式的修改结果
5. 在聊天中显示修改详情
6. 用户选择批量或单独接受/拒绝
7. 接受的修改应用到章节内容

### 6. 技术实现

#### 6.1 状态管理
```dart
final RxBool _isCreativeMode = false.obs;
final RxList<TextModification> _pendingModifications = <TextModification>[].obs;
final RxString _originalChapterContent = ''.obs;
```

#### 6.2 数据模型
- **ChapterEditSuggestion**: 聊天模式建议
- **TextModification**: 创作模式修改

#### 6.3 AI提示词
- **聊天模式**: 生成建议的提示词
- **创作模式**: 执行修改的提示词

#### 6.4 JSON解析
- 自动识别响应类型（建议/修改）
- 解析并创建相应的数据对象
- 错误处理和容错机制

### 7. 用户体验优化

#### 7.1 视觉设计
- **模式切换器**: 清晰的视觉区分
- **消息气泡**: 不同颜色标识不同类型
- **修改卡片**: 直观的原文/修改对比
- **操作按钮**: 明确的颜色编码（绿色接受/红色拒绝）

#### 7.2 交互反馈
- **模式切换**: 即时的状态提示
- **操作确认**: 成功/失败的snackbar提示
- **加载状态**: 生成过程中的loading指示
- **错误处理**: 友好的错误信息

#### 7.3 性能优化
- **响应式更新**: 使用Obx进行状态监听
- **内存管理**: 及时清理不需要的数据
- **界面流畅**: 避免阻塞UI的操作

### 8. 安全性和可靠性

#### 8.1 数据保护
- **原始内容备份**: 修改前自动备份
- **可逆操作**: 支持拒绝和恢复
- **状态隔离**: 不同模式的状态独立管理

#### 8.2 错误处理
- **JSON解析失败**: 友好的错误提示
- **网络异常**: 重试机制和错误反馈
- **修改冲突**: 位置验证和安全应用

### 9. 使用指南

#### 9.1 聊天模式使用
1. 确保在聊天模式（默认）
2. 点击"生成修改建议"按钮
3. 查看AI生成的建议
4. 选择性接受有用的建议

#### 9.2 创作模式使用
1. 切换到创作模式
2. 输入具体的编辑指令
3. 查看AI执行的修改
4. 批量或单独确认修改

### 10. 注意事项

#### 10.1 使用建议
- **明确指令**: 创作模式下使用具体明确的指令
- **分步操作**: 复杂修改建议分步进行
- **及时保存**: 确认修改后及时保存章节

#### 10.2 限制说明
- **AI理解**: 复杂指令可能需要多次尝试
- **内容质量**: 修改质量取决于AI模型能力
- **网络依赖**: 需要稳定的网络连接

## 总结

双模式功能为用户提供了灵活的AI辅助选择：
- **聊天模式**: 适合需要建议和指导的场景
- **创作模式**: 适合需要直接编辑协助的场景

通过清晰的界面设计和直观的交互流程，用户可以根据具体需求选择最适合的工作模式，提高创作效率和质量。
