// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'memory_chunk.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MemoryChunkTypeAdapter extends TypeAdapter<MemoryChunkType> {
  @override
  final int typeId = 11;

  @override
  MemoryChunkType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MemoryChunkType.worldSetting;
      case 1:
        return MemoryChunkType.character;
      case 2:
        return MemoryChunkType.location;
      case 3:
        return MemoryChunkType.plot;
      case 4:
        return MemoryChunkType.dialogue;
      case 5:
        return MemoryChunkType.scene;
      case 6:
        return MemoryChunkType.foreshadowing;
      case 7:
        return MemoryChunkType.relationship;
      case 8:
        return MemoryChunkType.item;
      case 9:
        return MemoryChunkType.timeline;
      case 10:
        return MemoryChunkType.theme;
      case 11:
        return MemoryChunkType.other;
      default:
        return MemoryChunkType.other;
    }
  }

  @override
  void write(BinaryWriter writer, MemoryChunkType obj) {
    switch (obj) {
      case MemoryChunkType.worldSetting:
        writer.writeByte(0);
        break;
      case MemoryChunkType.character:
        writer.writeByte(1);
        break;
      case MemoryChunkType.location:
        writer.writeByte(2);
        break;
      case MemoryChunkType.plot:
        writer.writeByte(3);
        break;
      case MemoryChunkType.dialogue:
        writer.writeByte(4);
        break;
      case MemoryChunkType.scene:
        writer.writeByte(5);
        break;
      case MemoryChunkType.foreshadowing:
        writer.writeByte(6);
        break;
      case MemoryChunkType.relationship:
        writer.writeByte(7);
        break;
      case MemoryChunkType.item:
        writer.writeByte(8);
        break;
      case MemoryChunkType.timeline:
        writer.writeByte(9);
        break;
      case MemoryChunkType.theme:
        writer.writeByte(10);
        break;
      case MemoryChunkType.other:
        writer.writeByte(11);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MemoryChunkTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MemoryImportanceAdapter extends TypeAdapter<MemoryImportance> {
  @override
  final int typeId = 12;

  @override
  MemoryImportance read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MemoryImportance.low;
      case 1:
        return MemoryImportance.medium;
      case 2:
        return MemoryImportance.high;
      case 3:
        return MemoryImportance.critical;
      default:
        return MemoryImportance.medium;
    }
  }

  @override
  void write(BinaryWriter writer, MemoryImportance obj) {
    switch (obj) {
      case MemoryImportance.low:
        writer.writeByte(0);
        break;
      case MemoryImportance.medium:
        writer.writeByte(1);
        break;
      case MemoryImportance.high:
        writer.writeByte(2);
        break;
      case MemoryImportance.critical:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MemoryImportanceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MemoryChunkAdapter extends TypeAdapter<MemoryChunk> {
  @override
  final int typeId = 10;

  @override
  MemoryChunk read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MemoryChunk(
      id: fields[0] as String,
      content: fields[1] as String,
      type: fields[2] as MemoryChunkType,
      importance: fields[3] as MemoryImportance,
      timestamp: fields[4] as DateTime,
      tags: (fields[5] as List?)?.cast<String>() ?? [],
      embedding: (fields[6] as List?)?.cast<double>(),
      chapterNumber: fields[7] as int?,
      characterName: fields[8] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, MemoryChunk obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.content)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.importance)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.tags)
      ..writeByte(6)
      ..write(obj.embedding)
      ..writeByte(7)
      ..write(obj.chapterNumber)
      ..writeByte(8)
      ..write(obj.characterName);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MemoryChunkAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
