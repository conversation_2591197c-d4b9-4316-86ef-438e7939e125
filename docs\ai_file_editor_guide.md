# AI文件编辑器使用指南

## 概述

AI文件编辑器是一个类似于Cursor IDE的AI驱动文件编辑功能，允许用户通过自然语言指令让AI直接编辑文件内容。

## 主要功能

### 1. 文件编辑
- **智能编辑**: 通过自然语言指令让AI编辑文件
- **实时预览**: 查看AI建议的修改内容
- **差异对比**: 清晰显示文件修改前后的差异
- **应用/拒绝**: 用户可以选择应用或拒绝AI的编辑建议

### 2. 编辑历史
- **历史记录**: 保存所有编辑操作的历史
- **撤销功能**: 可以撤销最近的编辑操作
- **备份机制**: 自动创建文件备份，确保数据安全

### 3. 智能识别
- **编辑意图识别**: 自动识别用户消息是否为文件编辑请求
- **上下文理解**: AI能够理解文件内容和编辑上下文
- **多种文件格式**: 支持文本、代码、Markdown等多种文件格式

## 使用方法

### 1. 访问AI文件编辑器
1. 打开应用
2. 进入"工具广场"
3. 点击"AI文件编辑器"

### 2. 选择文件
1. 点击"创建演示文件"按钮创建测试文件
2. 或者点击文件选择按钮选择现有文件
3. 文件打开后会显示在界面顶部

### 3. 发送编辑指令
1. 在指令输入框中输入编辑要求
2. 例如：
   - "添加更多注释"
   - "重构这段代码"
   - "修复语法错误"
   - "优化性能"
3. 点击"请求AI编辑"按钮

### 4. 查看和应用修改
1. AI处理完成后会显示编辑预览
2. 查看文件差异对比
3. 选择"应用修改"或"拒绝"
4. 应用后文件内容会立即更新

### 5. 管理编辑历史
1. 查看底部的编辑历史
2. 点击"撤销上次编辑"可以撤销最近的修改
3. 所有编辑都有自动备份

## 在Smart Composer中使用

### 1. 集成编辑功能
- Smart Composer聊天界面已集成文件编辑功能
- 点击文件选择按钮选择要编辑的文件
- 在聊天中发送编辑指令

### 2. 编辑指令示例
```
编辑文件：添加函数注释
修改文件：重构这个类的结构
更新文件：添加错误处理逻辑
优化代码：提高这段代码的性能
```

### 3. 自动识别
系统会自动识别包含以下关键词的消息为编辑请求：
- 编辑文件
- 修改文件
- 更新文件
- 改写
- 重构
- 优化代码

## 技术架构

### 核心组件

1. **FileEditService**: 文件操作服务
   - 读取和写入文件
   - 创建备份
   - 计算文件差异

2. **AIFileEditorController**: AI编辑控制器
   - 管理编辑状态
   - 处理AI编辑请求
   - 维护编辑历史

3. **SmartComposerController**: 增强的聊天控制器
   - 集成文件编辑功能
   - 识别编辑意图
   - 处理编辑消息

4. **FileEditPreviewWidget**: 编辑预览组件
   - 显示编辑建议
   - 文件差异对比
   - 应用/拒绝操作

### 数据流程

1. 用户选择文件 → 文件内容加载到控制器
2. 用户发送编辑指令 → AI分析并生成修改建议
3. 系统显示编辑预览 → 用户确认或拒绝
4. 应用修改 → 更新文件内容并记录历史

## 配置要求

### AI模型配置
- 需要在Smart Composer设置中配置AI模型
- 支持OpenAI、Anthropic、Google Gemini等多种模型
- 建议使用较新的模型以获得更好的编辑效果

### 文件权限
- 需要文件读写权限
- 支持本地文件系统访问
- 自动创建备份文件

## 最佳实践

### 1. 编辑指令
- 使用清晰、具体的指令
- 描述期望的修改结果
- 提供足够的上下文信息

### 2. 文件管理
- 定期清理备份文件
- 重要文件建议手动备份
- 使用版本控制系统

### 3. 安全考虑
- 谨慎编辑重要文件
- 先在测试文件上验证功能
- 及时撤销不满意的修改

## 故障排除

### 常见问题

1. **文件无法打开**
   - 检查文件路径是否正确
   - 确认文件权限
   - 尝试使用绝对路径

2. **AI编辑失败**
   - 检查AI模型配置
   - 确认网络连接
   - 尝试简化编辑指令

3. **编辑预览不显示**
   - 刷新界面
   - 检查控制器状态
   - 重新发送编辑请求

### 调试信息
- 查看控制台日志
- 检查错误提示
- 联系技术支持

## 更新日志

### v1.0.0
- 初始版本发布
- 基础文件编辑功能
- Smart Composer集成
- 编辑历史和撤销功能

## 反馈和建议

如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：
- 在应用内提交反馈
- 发送邮件至技术支持
- 参与社区讨论

---

*本文档会随着功能更新而持续更新，请关注最新版本。*
