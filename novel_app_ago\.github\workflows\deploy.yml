name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.9'
          channel: 'stable'
      
      - name: Enable web
        run: flutter config --enable-web
        
      - name: Get dependencies
        run: flutter pub get
        
      - name: Build
        run: flutter build web --release --base-href /novel_app/
        
      - name: Deploy
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./build/web 