import 'dart:io';
import 'dart:convert';
import '../models/novel.dart';
import '../services/ai_service.dart';
import '../services/embedding_service.dart';
import '../services/novel_file_manager.dart';
import '../services/novel_agent_service.dart';

/// 小说 Agent 工具系统
/// 实现类似 Cursor IDE 的工具集合，专门用于小说创作
class NovelAgentToolSystem {
  final AIService aiService;
  final EmbeddingService embeddingService;
  final NovelFileManager fileManager;
  
  NovelAgentToolSystem({
    required this.aiService,
    required this.embeddingService,
    required this.fileManager,
  });

  /// 内容分析工具
  Future<StepResult> analyzeContent(AgentContext context) async {
    try {
      final chapter = context.currentChapter;
      if (chapter == null) {
        return StepResult(
          response: '没有选择要分析的章节',
          editSuggestions: [],
        );
      }
      
      final systemPrompt = '''
你是一个专业的小说内容分析师。请分析以下章节内容，从以下维度给出分析：

1. 情节发展：分析情节推进是否合理
2. 人物塑造：评估人物形象是否鲜明
3. 文字表达：评价语言风格和表达效果
4. 结构安排：分析章节结构是否合理
5. 改进建议：提出具体的改进方向

请给出简洁而专业的分析报告。
''';
      
      final userPrompt = '''
小说信息：
标题：${context.novel.title}
类型：${context.novel.genre}

章节信息：
第${chapter.number}章：${chapter.title}

章节内容：
${chapter.content}
''';
      
      final analysis = await aiService.generateText(
        systemPrompt: systemPrompt,
        userPrompt: userPrompt,
        temperature: 0.3,
        maxTokens: 1500,
      );
      
      return StepResult(
        response: '## 章节分析报告\n\n$analysis',
        editSuggestions: [],
      );
      
    } catch (e) {
      return StepResult(
        response: '内容分析失败: $e',
        editSuggestions: [],
      );
    }
  }

  /// 编辑生成工具
  Future<StepResult> generateEdit(AgentContext context) async {
    try {
      final chapter = context.currentChapter;
      if (chapter == null) {
        return StepResult(
          response: '没有选择要编辑的章节',
          editSuggestions: [],
        );
      }
      
      final systemPrompt = '''
你是一个专业的小说编辑助手。根据用户的编辑指令，分析章节内容并生成具体的编辑建议。

编辑建议格式：
1. 明确指出需要修改的具体位置（行号范围）
2. 说明修改原因
3. 提供修改后的文本
4. 确保修改后的内容与整体风格一致

请生成详细的编辑建议，包括具体的修改位置和内容。
''';
      
      final userPrompt = '''
编辑指令：${context.intent.operation}

小说信息：
标题：${context.novel.title}
类型：${context.novel.genre}

当前章节：
第${chapter.number}章：${chapter.title}

章节内容：
${chapter.content}

${_buildReferencedChaptersContext(context.referencedChapters)}
''';
      
      final response = await aiService.generateText(
        systemPrompt: systemPrompt,
        userPrompt: userPrompt,
        temperature: 0.5,
        maxTokens: 2000,
      );
      
      // 解析编辑建议
      final editSuggestions = await _parseEditSuggestions(response, chapter);
      
      return StepResult(
        response: response,
        editSuggestions: editSuggestions,
      );
      
    } catch (e) {
      return StepResult(
        response: '生成编辑建议失败: $e',
        editSuggestions: [],
      );
    }
  }

  /// 内容生成工具
  Future<StepResult> generateContent(AgentContext context) async {
    try {
      final systemPrompt = '''
你是一个专业的小说创作助手。根据用户的要求生成高质量的小说内容。

创作要求：
1. 保持与现有内容的风格一致
2. 确保情节发展合理
3. 人物性格要符合已有设定
4. 语言表达要生动有趣
5. 内容要推动情节发展

请生成符合要求的小说内容。
''';
      
      final userPrompt = '''
创作指令：${context.intent.operation}

小说信息：
标题：${context.novel.title}
类型：${context.novel.genre}
大纲：${context.novel.outline}

${_buildCurrentChapterContext(context.currentChapter)}
${_buildReferencedChaptersContext(context.referencedChapters)}
''';
      
      final content = await aiService.generateText(
        systemPrompt: systemPrompt,
        userPrompt: userPrompt,
        temperature: 0.7,
        maxTokens: 3000,
      );
      
      return StepResult(
        response: '## 生成的内容\n\n$content',
        editSuggestions: [],
      );
      
    } catch (e) {
      return StepResult(
        response: '内容生成失败: $e',
        editSuggestions: [],
      );
    }
  }

  /// 上下文收集工具
  Future<StepResult> gatherContext(AgentContext context) async {
    try {
      final contextInfo = StringBuffer();
      
      contextInfo.writeln('## 上下文信息收集');
      contextInfo.writeln();
      
      // 小说基本信息
      contextInfo.writeln('### 小说信息');
      contextInfo.writeln('- 标题：${context.novel.title}');
      contextInfo.writeln('- 类型：${context.novel.genre}');
      contextInfo.writeln('- 总章节数：${context.novel.chapters.length}');
      contextInfo.writeln();
      
      // 当前章节信息
      if (context.currentChapter != null) {
        final chapter = context.currentChapter!;
        contextInfo.writeln('### 当前章节');
        contextInfo.writeln('- 第${chapter.number}章：${chapter.title}');
        contextInfo.writeln('- 字数：${chapter.content.length}');
        contextInfo.writeln();
      }
      
      // 引用章节信息
      if (context.referencedChapters.isNotEmpty) {
        contextInfo.writeln('### 引用章节');
        for (final chapter in context.referencedChapters) {
          contextInfo.writeln('- 第${chapter.number}章：${chapter.title}');
        }
        contextInfo.writeln();
      }
      
      // 相关章节信息
      if (context.relevantChapters.isNotEmpty) {
        contextInfo.writeln('### 相关章节');
        for (final chapter in context.relevantChapters) {
          contextInfo.writeln('- 第${chapter.number}章：${chapter.title}');
        }
        contextInfo.writeln();
      }
      
      return StepResult(
        response: contextInfo.toString(),
        editSuggestions: [],
      );
      
    } catch (e) {
      return StepResult(
        response: '收集上下文失败: $e',
        editSuggestions: [],
      );
    }
  }

  /// 语义搜索工具
  Future<List<Chapter>> searchRelevantChapters({
    required Novel novel,
    required String query,
    int topK = 3,
  }) async {
    try {
      // 使用嵌入服务搜索相关内容
      final searchResults = await embeddingService.searchSimilarContent(
        query: query,
        topK: topK,
      );
      
      final relevantChapters = <Chapter>[];
      
      for (final result in searchResults) {
        // 根据搜索结果找到对应的章节
        Chapter? chapter;
        try {
          chapter = novel.chapters.firstWhere(
            (c) => c.content.contains(result.content.substring(0, 50)),
          );
        } catch (e) {
          chapter = null;
        }
        
        if (chapter != null && !relevantChapters.contains(chapter)) {
          relevantChapters.add(chapter);
        }
      }
      
      return relevantChapters;
      
    } catch (e) {
      print('语义搜索失败: $e');
      return [];
    }
  }

  /// 文件读取工具
  Future<String> readChapterFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsString();
      } else {
        throw Exception('文件不存在: $filePath');
      }
    } catch (e) {
      throw Exception('读取文件失败: $e');
    }
  }

  /// 文件写入工具
  Future<void> writeChapterFile(String filePath, String content) async {
    try {
      final file = File(filePath);
      await file.writeAsString(content);
    } catch (e) {
      throw Exception('写入文件失败: $e');
    }
  }

  /// 解析编辑建议
  Future<List<EditSuggestion>> _parseEditSuggestions(
    String response,
    Chapter chapter,
  ) async {
    final suggestions = <EditSuggestion>[];
    
    try {
      // 使用 AI 解析编辑建议
      final systemPrompt = '''
你是一个编辑建议解析器。请从以下文本中提取具体的编辑建议，并返回结构化的JSON格式。

每个编辑建议应包含：
- description: 编辑描述
- startLine: 开始行号（从0开始）
- endLine: 结束行号（从0开始）
- originalText: 原始文本
- suggestedText: 建议文本
- type: 编辑类型（replace/insert/delete）

返回JSON数组格式：
[
  {
    "description": "编辑描述",
    "startLine": 0,
    "endLine": 2,
    "originalText": "原始文本",
    "suggestedText": "建议文本",
    "type": "replace"
  }
]
''';
      
      final parseResponse = await aiService.generateText(
        systemPrompt: systemPrompt,
        userPrompt: response,
        temperature: 0.1,
        maxTokens: 1000,
      );
      
      // 提取 JSON 部分
      final jsonMatch = RegExp(r'\[.*\]', dotAll: true).firstMatch(parseResponse);
      if (jsonMatch != null) {
        final jsonStr = jsonMatch.group(0)!;
        final List<dynamic> data = json.decode(jsonStr);
        
        for (final item in data) {
          suggestions.add(EditSuggestion(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            description: item['description'] ?? '',
            originalText: item['originalText'] ?? '',
            suggestedText: item['suggestedText'] ?? '',
            startLine: item['startLine'] ?? 0,
            endLine: item['endLine'] ?? 0,
            targetChapter: chapter,
            type: _parseEditType(item['type']),
          ));
        }
      }
      
    } catch (e) {
      print('解析编辑建议失败: $e');
      
      // 如果解析失败，创建一个通用的编辑建议
      suggestions.add(EditSuggestion(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        description: 'AI 编辑建议',
        originalText: chapter.content,
        suggestedText: response,
        startLine: 0,
        endLine: chapter.content.split('\n').length - 1,
        targetChapter: chapter,
        type: EditType.replace,
      ));
    }
    
    return suggestions;
  }

  /// 构建当前章节上下文
  String _buildCurrentChapterContext(Chapter? chapter) {
    if (chapter == null) return '';
    
    return '''
当前章节：
第${chapter.number}章：${chapter.title}
内容：
${chapter.content}
''';
  }

  /// 构建引用章节上下文
  String _buildReferencedChaptersContext(List<Chapter> chapters) {
    if (chapters.isEmpty) return '';
    
    final context = StringBuffer();
    context.writeln('引用章节：');
    
    for (final chapter in chapters) {
      context.writeln('第${chapter.number}章：${chapter.title}');
      context.writeln('内容摘要：${chapter.content.substring(0, 200)}...');
      context.writeln();
    }
    
    return context.toString();
  }

  /// 解析编辑类型
  EditType _parseEditType(String? type) {
    switch (type?.toLowerCase()) {
      case 'insert': return EditType.insert;
      case 'delete': return EditType.delete;
      case 'replace':
      default:
        return EditType.replace;
    }
  }
}


