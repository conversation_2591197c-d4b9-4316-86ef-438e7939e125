# Memory参数传递和章节引用功能说明

## 功能概述

为岱宗AI辅助助手添加了两个重要功能：
1. **LangChain Memory参数传递**: 使用memory参数而不是显式传递章节内容
2. **章节引用功能**: 用户可以使用@语法引用其他章节

## 1. Memory参数传递

### 实现原理

将章节内容和上下文信息通过memory参数传递给AI，而不是在提示词中显式包含：

```dart
// 构建memory上下文
Map<String, dynamic> memoryContext = {
  'novel_info': {
    'title': novel.title,
    'genre': novel.genre,
    'outline': novel.outline,
  },
  'current_chapter': {
    'number': chapter.number,
    'title': chapter.title,
    'content': chapter.content,
  },
  'referenced_chapters': [...],
  'context_info': {...},
};
```

### 技术实现

#### SmartComposerService改进
```dart
Future<String> sendChatMessage({
  required ChatModel model,
  required LLMProvider provider,
  required List<ChatMessage> messages,
  String? systemPrompt,
  Map<String, dynamic>? memoryContext, // 新增memory参数
  double temperature = 0.7,
  int maxTokens = 2000,
}) async
```

#### Memory上下文构建
```dart
String _buildMemoryContextPrompt(Map<String, dynamic> memoryContext) {
  // 将memory上下文转换为结构化的系统提示
  // 包含小说信息、当前章节、引用章节等
}
```

### 优势

1. **清晰分离**: 业务逻辑与提示词分离
2. **结构化数据**: 使用结构化的数据传递上下文
3. **可扩展性**: 易于添加新的上下文信息
4. **一致性**: 统一的上下文管理方式

## 2. 章节引用功能（@语法）

### 使用方法

用户可以在消息中使用@语法引用其他章节：

```
@第1章 请参考第一章的写作风格来优化当前章节
@1 删除类似第1章中的环境描写
@第3章 @第5章 结合这两章的情节来续写
```

### 支持的引用格式

- `@第X章`: 标准格式
- `@X`: 简化格式
- `@第X章 @第Y章`: 多章节引用

### 技术实现

#### 引用解析
```dart
Map<String, dynamic> _parseChapterReferences(String text) {
  final referencedChapters = <Chapter>[];
  String cleanedText = text;
  
  // 匹配 @第X章 或 @X 的模式
  final chapterPattern = RegExp(r'@(?:第)?(\d+)(?:章)?');
  final matches = chapterPattern.allMatches(text);
  
  for (final match in matches) {
    final chapterNumber = int.tryParse(match.group(1) ?? '');
    if (chapterNumber != null) {
      // 查找对应章节并添加到引用列表
      final chapterIndex = _currentNovel.chapters.indexWhere(
        (c) => c.number == chapterNumber,
      );
      
      if (chapterIndex != -1) {
        referencedChapters.add(_currentNovel.chapters[chapterIndex]);
      }
    }
    
    // 从文本中移除引用标记
    cleanedText = cleanedText.replaceAll(match.group(0)!, '');
  }
  
  return {
    'text': cleanedText,
    'chapters': referencedChapters,
  };
}
```

#### UI显示
```dart
// 引用章节显示组件
Obx(() {
  if (_referencedChapters.isEmpty) return const SizedBox.shrink();
  
  return Container(
    decoration: BoxDecoration(
      color: Colors.blue.withOpacity(0.1),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.blue.withOpacity(0.3)),
    ),
    child: Column(
      children: [
        // 标题和清除按钮
        Row(
          children: [
            Icon(Icons.link, color: Colors.blue[700]),
            Text('引用章节：'),
            Spacer(),
            IconButton(onPressed: () => _referencedChapters.clear()),
          ],
        ),
        // 章节标签
        Wrap(
          children: _referencedChapters.map((chapter) => 
            Container(
              child: Text('第${chapter.number}章：${chapter.title}'),
            )
          ).toList(),
        ),
      ],
    ),
  );
})
```

## 3. 创作模式集成

### 提示词增强

引用章节会自动添加到AI提示词中：

```dart
String _buildContinueWritingPrompt(String instruction, Chapter chapter, String content, [List<Chapter>? referencedChapters]) {
  final buffer = StringBuffer();
  
  buffer.writeln('章节标题：${chapter.title}');
  buffer.writeln('当前内容：');
  buffer.writeln(content);
  
  // 添加引用章节信息
  if (referencedChapters != null && referencedChapters.isNotEmpty) {
    buffer.writeln('=== 引用章节（作为参考） ===');
    for (final refChapter in referencedChapters) {
      buffer.writeln('第${refChapter.number}章：${refChapter.title}');
      final refContent = refChapter.content.length > 500 
          ? '${refChapter.content.substring(0, 500)}...'
          : refChapter.content;
      buffer.writeln(refContent);
    }
  }
  
  buffer.writeln('用户指令：$instruction');
  // ... JSON格式要求
}
```

### 智能内容限制

为避免上下文过长，引用章节内容会被智能截断：
- 续写提示：每个引用章节最多500字符
- 删除提示：每个引用章节最多300字符
- 通用编辑：每个引用章节最多400字符

## 4. 使用场景

### 续写场景
```
@第1章 @第3章 参考这两章的风格和情节发展，续写当前章节
```

### 删除场景
```
@第2章 删除类似第2章中的环境描写部分
```

### 优化场景
```
@第1章 参考第1章的对话风格，优化当前章节的对话部分
```

### 风格统一
```
@第1章 @第2章 保持与前面章节一致的叙述风格
```

## 5. 用户界面

### 输入提示
输入框提示文本已更新：
- 创作模式：`输入编辑指令，如"删除环境描写"、"优化对话"等... 使用@第X章引用其他章节`
- 聊天模式：`询问岱宗AI助手... 使用@第X章引用其他章节`

### 引用显示
- 在输入框上方显示当前引用的章节
- 蓝色边框和背景，清晰标识
- 可以单独清除引用
- 显示章节编号和标题

### 交互体验
- 自动解析@引用
- 实时显示引用状态
- 一键清除所有引用
- 引用章节数量无限制

## 6. 技术优势

### Memory参数传递
1. **结构化**: 数据结构清晰，易于维护
2. **可扩展**: 容易添加新的上下文信息
3. **分离关注点**: 业务逻辑与AI提示分离
4. **统一接口**: 所有AI服务使用相同的memory接口

### 章节引用
1. **直观易用**: @语法简单直观
2. **智能解析**: 支持多种引用格式
3. **可视化**: 清晰显示引用状态
4. **上下文感知**: AI能理解章节间的关联

## 7. 最佳实践

### 引用章节
- 引用相关章节而不是所有章节
- 注意引用章节的内容长度
- 使用简洁的引用格式

### Memory使用
- 合理控制memory上下文的大小
- 包含必要的结构化信息
- 避免重复信息

### 性能考虑
- 引用章节内容会被自动截断
- Memory上下文会被缓存
- 避免过多的章节引用

## 8. 未来扩展

### 可能的改进
1. **智能章节推荐**: 根据内容自动推荐相关章节
2. **引用预览**: 鼠标悬停显示章节内容预览
3. **引用历史**: 记录常用的章节引用组合
4. **语义搜索**: 基于内容相似度的章节引用

### 技术扩展
1. **向量化存储**: 使用向量数据库存储章节内容
2. **智能摘要**: 自动生成章节摘要用于引用
3. **关联分析**: 分析章节间的关联关系
4. **个性化**: 根据用户习惯优化引用体验

## 总结

通过Memory参数传递和章节引用功能，岱宗AI辅助助手现在能够：

1. **更智能的上下文管理**: 使用结构化的memory参数传递上下文信息
2. **灵活的章节引用**: 用户可以轻松引用其他章节作为参考
3. **增强的创作体验**: AI能够理解章节间的关联和上下文
4. **清晰的用户界面**: 直观显示引用状态和上下文信息

这些功能大大提升了AI辅助写作的智能化程度和用户体验。
