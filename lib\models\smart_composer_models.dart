/// Smart Composer 相关的数据模型
/// 移植自 obsidian-smart-composer 项目

import 'package:novel_app/models/text_modification.dart';

/// LLM 提供商类型
enum LLMProviderType {
  openai('openai', 'OpenAI'),
  anthropic('anthropic', 'Anthropic'),
  gemini('gemini', 'Google Gemini'),
  groq('groq', 'Groq'),
  deepseek('deepseek', 'DeepSeek'),
  perplexity('perplexity', 'Perplexity'),
  mistral('mistral', 'Mistral'),
  openrouter('openrouter', 'OpenRouter'),
  ollama('ollama', 'Ollama'),
  lmStudio('lm-studio', 'LM Studio'),
  morph('morph', 'Morph'),
  azureOpenai('azure-openai', 'Azure OpenAI'),
  openaiCompatible('openai-compatible', 'OpenAI Compatible');

  const LLMProviderType(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// LLM 提供商配置
class LLMProvider {
  final String id;
  final LLMProviderType type;
  final String? apiKey;
  final String? baseUrl;
  final Map<String, dynamic>? additionalConfig;

  const LLMProvider({
    required this.id,
    required this.type,
    this.apiKey,
    this.baseUrl,
    this.additionalConfig,
  });

  LLMProvider copyWith({
    String? id,
    LLMProviderType? type,
    String? apiKey,
    String? baseUrl,
    Map<String, dynamic>? additionalConfig,
  }) {
    return LLMProvider(
      id: id ?? this.id,
      type: type ?? this.type,
      apiKey: apiKey ?? this.apiKey,
      baseUrl: baseUrl ?? this.baseUrl,
      additionalConfig: additionalConfig ?? this.additionalConfig,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type.value,
        'apiKey': apiKey,
        'baseUrl': baseUrl,
        'additionalConfig': additionalConfig,
      };

  factory LLMProvider.fromJson(Map<String, dynamic> json) {
    return LLMProvider(
      id: json['id'] as String,
      type: LLMProviderType.values.firstWhere(
        (e) => e.value == json['type'],
        orElse: () => LLMProviderType.openai,
      ),
      apiKey: json['apiKey'] as String?,
      baseUrl: json['baseUrl'] as String?,
      additionalConfig: json['additionalConfig'] != null
          ? Map<String, dynamic>.from(json['additionalConfig'] as Map)
          : null,
    );
  }
}

/// 聊天模型配置
class ChatModel {
  final String id;
  final String model;
  final String providerId;
  final LLMProviderType providerType;
  final Map<String, dynamic>? additionalConfig;

  const ChatModel({
    required this.id,
    required this.model,
    required this.providerId,
    required this.providerType,
    this.additionalConfig,
  });

  ChatModel copyWith({
    String? id,
    String? model,
    String? providerId,
    LLMProviderType? providerType,
    Map<String, dynamic>? additionalConfig,
  }) {
    return ChatModel(
      id: id ?? this.id,
      model: model ?? this.model,
      providerId: providerId ?? this.providerId,
      providerType: providerType ?? this.providerType,
      additionalConfig: additionalConfig ?? this.additionalConfig,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'model': model,
        'providerId': providerId,
        'providerType': providerType.value,
        'additionalConfig': additionalConfig,
      };

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] as String,
      model: json['model'] as String,
      providerId: json['providerId'] as String,
      providerType: LLMProviderType.values.firstWhere(
        (e) => e.value == json['providerType'],
        orElse: () => LLMProviderType.openai,
      ),
      additionalConfig: json['additionalConfig'] != null
          ? Map<String, dynamic>.from(json['additionalConfig'] as Map)
          : null,
    );
  }
}

/// 聊天消息类型
enum ChatMessageType {
  normal,        // 普通聊天消息
  creativeEdit,  // 创作模式修改建议
  systemInfo,    // 系统信息
}

/// 聊天消息
class ChatMessage {
  final String id;
  final String role; // 'user', 'assistant', 'system'
  final String content;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
  final ChatMessageType messageType;
  final CreativeEditResponse? editResponse; // 创作模式的修改建议数据

  const ChatMessage({
    required this.id,
    required this.role,
    required this.content,
    required this.timestamp,
    this.metadata,
    this.messageType = ChatMessageType.normal,
    this.editResponse,
  });

  ChatMessage copyWith({
    String? id,
    String? role,
    String? content,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
    ChatMessageType? messageType,
    CreativeEditResponse? editResponse,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      role: role ?? this.role,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
      messageType: messageType ?? this.messageType,
      editResponse: editResponse ?? this.editResponse,
    );
  }

  /// 是否为创作模式修改建议
  bool get isCreativeEdit => messageType == ChatMessageType.creativeEdit;

  /// 是否有待处理的修改
  bool get hasPendingModifications =>
      editResponse?.hasPendingModifications ?? false;

  Map<String, dynamic> toJson() => {
        'id': id,
        'role': role,
        'content': content,
        'timestamp': timestamp.toIso8601String(),
        'metadata': metadata,
        'messageType': messageType.name,
        'editResponse': editResponse?.toJson(),
      };

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      role: json['role'] as String,
      content: json['content'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      messageType: ChatMessageType.values.firstWhere(
        (type) => type.name == json['messageType'],
        orElse: () => ChatMessageType.normal,
      ),
      editResponse: json['editResponse'] != null
          ? CreativeEditResponse.fromJson(Map<String, dynamic>.from(json['editResponse'] as Map))
          : null,
    );
  }
}

/// 聊天会话
class ChatSession {
  final String id;
  final String title;
  final List<ChatMessage> messages;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? novelId; // 关联的小说ID
  final int? chapterNumber; // 关联的章节号
  final Map<String, dynamic>? context; // 上下文信息

  const ChatSession({
    required this.id,
    required this.title,
    required this.messages,
    required this.createdAt,
    this.updatedAt,
    this.novelId,
    this.chapterNumber,
    this.context,
  });

  ChatSession copyWith({
    String? id,
    String? title,
    List<ChatMessage>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? novelId,
    int? chapterNumber,
    Map<String, dynamic>? context,
  }) {
    return ChatSession(
      id: id ?? this.id,
      title: title ?? this.title,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      novelId: novelId ?? this.novelId,
      chapterNumber: chapterNumber ?? this.chapterNumber,
      context: context ?? this.context,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'messages': messages.map((m) => m.toJson()).toList(),
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
        'novelId': novelId,
        'chapterNumber': chapterNumber,
        'context': context,
      };

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'] as String,
      title: json['title'] as String,
      messages: (json['messages'] as List? ?? [])
          .map((m) => ChatMessage.fromJson(Map<String, dynamic>.from(m as Map)))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      novelId: json['novelId'] as String?,
      chapterNumber: json['chapterNumber'] as int?,
      context: json['context'] != null
          ? Map<String, dynamic>.from(json['context'] as Map)
          : null,
    );
  }
}

/// Smart Composer 设置
class SmartComposerSettings {
  final List<LLMProvider> providers;
  final List<ChatModel> chatModels;
  final String? defaultChatModelId;
  final String? systemPrompt;
  final Map<String, dynamic>? additionalSettings;

  const SmartComposerSettings({
    required this.providers,
    required this.chatModels,
    this.defaultChatModelId,
    this.systemPrompt,
    this.additionalSettings,
  });

  SmartComposerSettings copyWith({
    List<LLMProvider>? providers,
    List<ChatModel>? chatModels,
    String? defaultChatModelId,
    String? systemPrompt,
    Map<String, dynamic>? additionalSettings,
  }) {
    return SmartComposerSettings(
      providers: providers ?? this.providers,
      chatModels: chatModels ?? this.chatModels,
      defaultChatModelId: defaultChatModelId ?? this.defaultChatModelId,
      systemPrompt: systemPrompt ?? this.systemPrompt,
      additionalSettings: additionalSettings ?? this.additionalSettings,
    );
  }

  Map<String, dynamic> toJson() => {
        'providers': providers.map((p) => p.toJson()).toList(),
        'chatModels': chatModels.map((m) => m.toJson()).toList(),
        'defaultChatModelId': defaultChatModelId,
        'systemPrompt': systemPrompt,
        'additionalSettings': additionalSettings,
      };

  factory SmartComposerSettings.fromJson(Map<String, dynamic> json) {
    return SmartComposerSettings(
      providers: (json['providers'] as List? ?? [])
          .map((p) => LLMProvider.fromJson(Map<String, dynamic>.from(p as Map)))
          .toList(),
      chatModels: (json['chatModels'] as List? ?? [])
          .map((m) => ChatModel.fromJson(Map<String, dynamic>.from(m as Map)))
          .toList(),
      defaultChatModelId: json['defaultChatModelId'] as String?,
      systemPrompt: json['systemPrompt'] as String?,
      additionalSettings: json['additionalSettings'] != null
          ? Map<String, dynamic>.from(json['additionalSettings'] as Map)
          : null,
    );
  }
}

/// 默认配置
class SmartComposerDefaults {
  static const List<LLMProvider> defaultProviders = [
    LLMProvider(
      id: 'openai-default',
      type: LLMProviderType.openai,
    ),
    LLMProvider(
      id: 'anthropic-default',
      type: LLMProviderType.anthropic,
    ),
    LLMProvider(
      id: 'gemini-default',
      type: LLMProviderType.gemini,
    ),
    LLMProvider(
      id: 'deepseek-default',
      type: LLMProviderType.deepseek,
    ),
  ];

  static const List<ChatModel> defaultChatModels = [
    ChatModel(
      id: 'gpt-4o',
      model: 'gpt-4o',
      providerId: 'openai-default',
      providerType: LLMProviderType.openai,
    ),
    ChatModel(
      id: 'gpt-4o-mini',
      model: 'gpt-4o-mini',
      providerId: 'openai-default',
      providerType: LLMProviderType.openai,
    ),
    ChatModel(
      id: 'claude-3-5-sonnet',
      model: 'claude-3-5-sonnet-latest',
      providerId: 'anthropic-default',
      providerType: LLMProviderType.anthropic,
    ),
    ChatModel(
      id: 'gemini-1.5-pro',
      model: 'gemini-1.5-pro',
      providerId: 'gemini-default',
      providerType: LLMProviderType.gemini,
    ),
    ChatModel(
      id: 'deepseek-chat',
      model: 'deepseek-chat',
      providerId: 'deepseek-default',
      providerType: LLMProviderType.deepseek,
    ),
  ];

  static const String defaultSystemPrompt = '''你是一个专业的小说写作助手。你的任务是帮助用户创作高质量的小说内容。

请遵循以下原则：
1. 保持故事的连贯性和逻辑性
2. 注重人物性格的一致性和发展
3. 使用生动的描写和对话
4. 根据用户的要求调整写作风格
5. 提供建设性的写作建议

当用户提供章节内容或大纲时，请仔细分析并基于这些信息进行创作。''';
}
